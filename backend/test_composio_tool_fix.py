#!/usr/bin/env python3
"""
Test script to verify the Composio tool call mapping fix.

This script tests the dynamic Composio tool registration and execution
to ensure the method name mismatch issue is resolved.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from utils.logger import logger
from agentpress.thread_manager import ThreadManager


async def test_composio_tool_registration():
    """Test that Composio tools are registered correctly with proper method names."""
    
    logger.info("🧪 Testing Composio tool registration fix...")
    
    # Test user ID from our test data
    test_user_id = "f7f2d110-7af2-41b1-a23c-3c6a3c866689"  # Gmail user
    
    try:
        # Create ThreadManager instance
        thread_manager = ThreadManager()
        
        # Load dynamic Composio tools
        logger.info(f"🔗 Loading dynamic Composio tools for user {test_user_id}")
        await thread_manager.load_composio_tools_dynamic(test_user_id)
        
        # Check registered tools
        xml_tools = thread_manager.tool_registry.xml_tools
        logger.info(f"📋 Found {len(xml_tools)} registered XML tools")
        
        # Validate each tool registration
        validation_errors = []
        for tag_name, tool_info in xml_tools.items():
            tool_instance = tool_info["instance"]
            method_name = tool_info["method"]
            
            logger.info(f"🔍 Validating tool: {tag_name}")
            logger.info(f"   - Instance: {tool_instance.__class__.__name__}")
            logger.info(f"   - Method: {method_name}")
            logger.info(f"   - Service: {tool_info.get('service_name', 'unknown')}")
            logger.info(f"   - Action: {tool_info.get('action_name', 'unknown')}")
            
            # Check if method exists
            if not hasattr(tool_instance, method_name):
                error_msg = f"❌ Tool {tag_name} missing method {method_name}"
                validation_errors.append(error_msg)
                logger.error(error_msg)
                
                # Show available methods for debugging
                available_methods = [m for m in dir(tool_instance) if not m.startswith('_')]
                logger.error(f"   Available methods: {available_methods}")
            else:
                logger.info(f"   ✅ Method {method_name} exists")
        
        # Test tool registry function lookup
        logger.info("🔍 Testing tool registry function lookup...")
        available_functions = thread_manager.tool_registry.get_available_functions()
        logger.info(f"📋 Tool registry returned {len(available_functions)} available functions")
        
        # Check for expected function names
        composio_functions = [name for name in available_functions.keys() if name == "execute"]
        logger.info(f"📋 Found {len(composio_functions)} Composio execute functions")
        
        if validation_errors:
            logger.error(f"❌ Validation failed with {len(validation_errors)} errors:")
            for error in validation_errors:
                logger.error(f"   {error}")
            return False
        else:
            logger.info("✅ All tools registered correctly!")
            return True
            
    except Exception as e:
        logger.error(f"❌ Test failed with exception: {e}", exc_info=True)
        return False


async def test_tool_execution_simulation():
    """Simulate tool execution to verify the fix works end-to-end."""
    
    logger.info("🧪 Testing tool execution simulation...")
    
    test_user_id = "f7f2d110-7af2-41b1-a23c-3c6a3c866689"
    
    try:
        thread_manager = ThreadManager()
        await thread_manager.load_composio_tools_dynamic(test_user_id)
        
        # Get available functions
        available_functions = thread_manager.tool_registry.get_available_functions()
        
        # Find a Composio tool function
        execute_functions = [name for name in available_functions.keys() if name == "execute"]
        
        if not execute_functions:
            logger.warning("⚠️ No execute functions found - cannot test execution")
            return False
        
        logger.info(f"🎯 Found {len(execute_functions)} execute functions to test")
        
        # Test that we can get the function without errors
        for func_name in execute_functions[:1]:  # Test just the first one
            try:
                func = available_functions[func_name]
                logger.info(f"✅ Successfully retrieved function: {func_name}")
                logger.info(f"   Function type: {type(func)}")
                logger.info(f"   Function callable: {callable(func)}")
                
                # Note: We don't actually call the function to avoid side effects
                # but we've verified the lookup works
                
            except Exception as e:
                logger.error(f"❌ Failed to retrieve function {func_name}: {e}")
                return False
        
        logger.info("✅ Tool execution simulation passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Execution simulation failed: {e}", exc_info=True)
        return False


async def main():
    """Run all tests."""
    
    logger.info("🚀 Starting Composio tool fix validation tests...")
    
    # Test 1: Tool registration
    registration_test = await test_composio_tool_registration()
    
    # Test 2: Tool execution simulation
    execution_test = await test_tool_execution_simulation()
    
    # Summary
    if registration_test and execution_test:
        logger.info("🎉 All tests passed! The Composio tool fix is working correctly.")
        return True
    else:
        logger.error("❌ Some tests failed. The fix may need additional work.")
        return False


if __name__ == "__main__":
    # Set up environment
    from dotenv import load_dotenv
    load_dotenv()
    
    # Run tests
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
