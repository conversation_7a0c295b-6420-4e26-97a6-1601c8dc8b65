from typing import Dict, Type, Any, List, Optional, Callable
from agentpress.tool import Tool, SchemaType
from utils.logger import logger
import importlib
import os
import logging
from pathlib import Path

logger = logging.getLogger(__name__)


class ToolRegistry:
    """Registry for managing and accessing tools.

    Maintains a collection of tool instances and their schemas, allowing for
    selective registration of tool functions and easy access to tool capabilities.

    Attributes:
        tools (Dict[str, Dict[str, Any]]): OpenAPI-style tools and schemas
        xml_tools (Dict[str, Dict[str, Any]]): XML-style tools and schemas

    Methods:
        register_tool: Register a tool with optional function filtering
        get_tool: Get a specific tool by name
        get_xml_tool: Get a tool by XML tag name
        get_openapi_schemas: Get OpenAPI schemas for function calling
        get_xml_examples: Get examples of XML tool usage
    """

    def __init__(self):
        """Initialize a new ToolRegistry instance."""
        self._tools = {}
        self._schemas = []
        logger.info("Initializing ToolRegistry")
        self._load_tools()
        self.xml_tools = {}
        logger.debug(
            f"Initialized ToolRegistry with {len(self._tools)} tools and {len(self._schemas)} schemas"
        )

    def _load_tools(self):
        """Load all tools from the agent/tools directory."""
        try:
            # Get the tools directory path
            tools_dir = Path(__file__).parent.parent / "agent" / "tools"
            logger.info(f"Loading tools from: {tools_dir}")
            if not tools_dir.exists():
                logger.error(f"Tools directory not found: {tools_dir}")
                return

            # List all Python files in the tools directory
            tool_files = [
                f
                for f in tools_dir.glob("*.py")
                if f.is_file() and f.name != "__init__.py"
            ]
            logger.info(
                f"Found {len(tool_files)} tool files: {[f.name for f in tool_files]}"
            )

            for tool_file in tool_files:
                try:
                    # Convert file path to module path
                    module_name = f"agent.tools.{tool_file.stem}"
                    logger.debug(f"Attempting to load module: {module_name}")

                    # Import the module
                    module = importlib.import_module(module_name)
                    logger.debug(f"Successfully imported module: {module_name}")

                    # Look for tool classes
                    tool_classes_found = 0
                    for attr_name in dir(module):
                        attr = getattr(module, attr_name)

                        # Check if it's a class that inherits from Tool
                        if (
                            isinstance(attr, type)
                            and issubclass(attr, Tool)
                            and attr != Tool
                        ):
                            try:
                                logger.debug(
                                    f"Found tool class: {attr_name} in {module_name}"
                                )

                                # Check if the tool requires constructor parameters
                                import inspect

                                init_signature = inspect.signature(attr.__init__)
                                required_params = [
                                    param
                                    for param_name, param in init_signature.parameters.items()
                                    if param_name != "self"
                                    and param.default == inspect.Parameter.empty
                                ]

                                if required_params:
                                    logger.debug(
                                        f"Tool {attr_name} requires constructor parameters: {[p.name for p in required_params]}. Skipping auto-registration."
                                    )
                                    continue

                                # Instantiate the tool (only if no required parameters)
                                tool_instance = attr()
                                schemas = tool_instance.get_schemas()
                                logger.debug(
                                    f"Tool class {attr_name} has {len(schemas)} schemas: {list(schemas.keys())}"
                                )

                                for func_name, schema_list in schemas.items():
                                    for schema in schema_list:
                                        if schema.schema_type == SchemaType.OPENAPI:
                                            logger.info(
                                                f"Registering OpenAPI tool: {func_name} from {tool_file.name}"
                                            )
                                            self._tools[func_name] = {
                                                "instance": tool_instance,
                                                "schema": schema,
                                            }
                                            schema_dict = {
                                                "type": "function",
                                                "function": {
                                                    "name": func_name,
                                                    "description": schema.schema.get(
                                                        "function", {}
                                                    ).get("description", ""),
                                                    "parameters": schema.schema.get(
                                                        "function", {}
                                                    ).get("parameters", {}),
                                                },
                                            }
                                            self._schemas.append(schema_dict)
                                            logger.debug(
                                                f"Added schema for {func_name}: {schema_dict}"
                                            )
                                tool_classes_found += 1
                            except Exception as tool_init_error:
                                logger.error(
                                    f"Error initializing tool {attr_name} from {tool_file}: {tool_init_error}",
                                    exc_info=True,
                                )
                                continue
                    logger.info(
                        f"Found {tool_classes_found} tool classes in {module_name}"
                    )

                except Exception as module_error:
                    logger.error(
                        f"Error loading module from {tool_file}: {module_error}",
                        exc_info=True,
                    )
                    continue

            logger.info(
                f"Successfully loaded {len(self._tools)} tools with {len(self._schemas)} schemas"
            )

        except Exception as e:
            logger.error(f"Error in tool loading process: {e}", exc_info=True)
            raise

    def register_tool(
        self,
        tool_class: Type[Tool],
        function_names: Optional[List[str]] = None,
        **kwargs,
    ):
        """Register a tool with optional function filtering.

        Args:
            tool_class: The tool class to register
            function_names: Optional list of specific functions to register
            **kwargs: Additional arguments passed to tool initialization

        Notes:
            - If function_names is None, all functions are registered
            - Handles both OpenAPI and XML schema registration
        """
        logger.debug(f"Registering tool class: {tool_class.__name__}")
        tool_instance = tool_class(**kwargs)
        schemas = tool_instance.get_schemas()

        logger.debug(
            f"Available schemas for {tool_class.__name__}: {list(schemas.keys())}"
        )

        registered_openapi = 0
        registered_xml = 0

        for func_name, schema_list in schemas.items():
            if function_names is None or func_name in function_names:
                for schema in schema_list:
                    if schema.schema_type == SchemaType.OPENAPI:
                        self._tools[func_name] = {
                            "instance": tool_instance,
                            "schema": schema,
                        }
                        registered_openapi += 1
                        logger.debug(
                            f"Registered OpenAPI function {func_name} from {tool_class.__name__}"
                        )

                    if schema.schema_type == SchemaType.XML and schema.xml_schema:
                        self.xml_tools[schema.xml_schema.tag_name] = {
                            "instance": tool_instance,
                            "method": func_name,
                            "schema": schema,
                        }
                        registered_xml += 1
                        logger.debug(
                            f"Registered XML tag {schema.xml_schema.tag_name} -> {func_name} from {tool_class.__name__}"
                        )

        logger.debug(
            f"Tool registration complete for {tool_class.__name__}: {registered_openapi} OpenAPI functions, {registered_xml} XML tags"
        )

    def get_available_functions(self) -> Dict[str, Callable]:
        """Get all available tool functions.

        Returns:
            Dict mapping function names to their implementations
        """
        available_functions = {}

        # Get OpenAPI tool functions
        for tool_name, tool_info in self._tools.items():
            tool_instance = tool_info["instance"]
            function_name = tool_name
            function = getattr(tool_instance, function_name)
            available_functions[function_name] = function

        # Get XML tool functions
        logger.info(
            f"🔍 Processing {len(self.xml_tools)} XML tools for function registration"
        )
        for tag_name, tool_info in self.xml_tools.items():
            tool_instance = tool_info["instance"]
            method_name = tool_info["method"]

            logger.info(
                f"🔧 Processing XML tool '{tag_name}' with method '{method_name}'"
            )
            logger.debug(f"   Tool instance: {tool_instance.__class__.__name__}")
            logger.debug(f"   Tool info keys: {list(tool_info.keys())}")

            # Validate that the method exists on the instance
            if not hasattr(tool_instance, method_name):
                logger.error(
                    f"❌ Tool instance {tool_instance.__class__.__name__} for tag '{tag_name}' "
                    f"does not have method '{method_name}'. Available methods: "
                    f"{[m for m in dir(tool_instance) if not m.startswith('_')]}"
                )
                continue

            try:
                function = getattr(tool_instance, method_name)
                # ✅ FIX: Use the method_name as the key, not a generated function name
                # This ensures the XML parser can find the correct function
                available_functions[method_name] = function
                logger.info(
                    f"✅ Registered function '{method_name}' for XML tag '{tag_name}'"
                )

                # ✅ ADDITIONAL: Also register with service-specific names for backward compatibility
                # This helps with any legacy function name lookups
                if "service_name" in tool_info and "action_name" in tool_info:
                    service_name = tool_info["service_name"]
                    action_name = tool_info["action_name"]
                    # Create a service-specific function name as fallback
                    service_function_name = f"execute_{service_name}_{action_name}"
                    available_functions[service_function_name] = function
                    logger.info(
                        f"✅ Also registered function '{service_function_name}' for backward compatibility"
                    )

            except Exception as e:
                logger.error(
                    f"❌ Failed to get method '{method_name}' from tool '{tag_name}': {e}"
                )
                continue

        logger.debug(f"Retrieved {len(available_functions)} available functions")
        return available_functions

    def get_tool(self, tool_name: str) -> Dict[str, Any]:
        """Get a specific tool by name.

        Args:
            tool_name: Name of the tool function

        Returns:
            Dict containing tool instance and schema, or empty dict if not found
        """
        tool = self._tools.get(tool_name, {})
        if not tool:
            logger.warning(f"Tool not found: {tool_name}")
        return tool

    def get_xml_tool(self, tag_name: str) -> Dict[str, Any]:
        """Get tool info by XML tag name.

        Args:
            tag_name: XML tag name for the tool

        Returns:
            Dict containing tool instance, method name, and schema
        """
        tool = self.xml_tools.get(tag_name, {})
        if not tool:
            logger.warning(f"XML tool not found for tag: {tag_name}")
        return tool

    def get_openapi_schemas(self) -> List[Dict[str, Any]]:
        """Get OpenAPI schemas for all registered tools."""
        logger.debug(f"Returning {len(self._schemas)} OpenAPI schemas")
        return self._schemas

    def get_xml_examples(self) -> Dict[str, str]:
        """Get all XML tag examples.

        Returns:
            Dict mapping tag names to their example usage
        """
        examples = {}
        for tool_info in self.xml_tools.values():
            schema = tool_info["schema"]
            if schema.xml_schema and schema.xml_schema.example:
                examples[schema.xml_schema.tag_name] = schema.xml_schema.example
        logger.debug(f"Retrieved {len(examples)} XML examples")
        return examples

    def list_tools(self) -> List[str]:
        """List all registered tool names."""
        tool_list = list(self._tools.keys())
        logger.debug(f"Listed {len(tool_list)} tools: {tool_list}")
        return tool_list
