#!/usr/bin/env python3
"""
Test script to verify the Composio tool fix is working correctly.
This script tests the tool registration and function mapping.
"""

import asyncio
import sys
import os

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


async def test_tool_registration():
    """Test that tools are registered correctly with the right method names."""
    print("🔍 Testing Composio tool registration fix...")

    try:
        from agentpress.thread_manager import ThreadManager

        # Create a ThreadManager instance
        thread_manager = ThreadManager()

        # Test user ID from the CSV file (valid UUID)
        test_user_id = "f7f2d110-7af2-41b1-a23c-3c6a3c866689"

        print(f"📋 Loading Composio tools for test user: {test_user_id}")

        # Load Composio tools dynamically
        await thread_manager.load_composio_tools_dynamic(test_user_id)

        # Check what XML tools are registered
        xml_tools = thread_manager.tool_registry.xml_tools
        print(f"\n📦 Found {len(xml_tools)} XML tools registered:")

        for tag_name, tool_info in xml_tools.items():
            if tag_name.endswith("-action"):  # Composio tools
                method_name = tool_info.get("method", "unknown")
                service_name = tool_info.get("service_name", "unknown")
                action_name = tool_info.get("action_name", "unknown")

                print(f"  ✅ {tag_name}")
                print(f"     Method: {method_name}")
                print(f"     Service: {service_name}")
                print(f"     Action: {action_name}")

                # Verify the method exists on the instance
                instance = tool_info.get("instance")
                if instance and hasattr(instance, method_name):
                    print(f"     ✅ Method '{method_name}' exists on instance")
                else:
                    print(f"     ❌ Method '{method_name}' NOT found on instance")

        # Test function registry
        print(f"\n🔧 Testing function registry...")
        available_functions = thread_manager.tool_registry.get_available_functions()

        composio_functions = [
            name
            for name in available_functions.keys()
            if name == "execute" or name.startswith("execute_")
        ]

        print(f"📋 Found {len(composio_functions)} Composio-related functions:")
        for func_name in sorted(composio_functions):
            print(f"  ✅ {func_name}")

        # Test specific function lookup
        if "execute" in available_functions:
            print(f"\n✅ SUCCESS: 'execute' method found in function registry")
            execute_func = available_functions["execute"]
            print(f"   Function: {execute_func}")
        else:
            print(f"\n❌ ERROR: 'execute' method NOT found in function registry")

        return True

    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback

        traceback.print_exc()
        return False


async def main():
    """Main test function."""
    print("🚀 Starting Composio tool fix verification...")

    success = await test_tool_registration()

    if success:
        print("\n🎉 Tool registration test completed successfully!")
        print("✅ The cross-wiring fix should now be working.")
    else:
        print("\n❌ Tool registration test failed!")
        print("🔧 The fix may need additional work.")


if __name__ == "__main__":
    asyncio.run(main())
